'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { Button } from '@/components/ui';
import { Section } from '@/components/layout/Section';
import { Logo } from '@/components/brand/Logo';
import { Card, CardContent } from '@/components/ui/Card';

export default function HomePage() {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <Section variant="primary" padding="xl" className="min-h-screen flex items-center">
        <div className="text-center space-y-8 max-w-4xl mx-auto">
          <Logo size="xl" showTagline animated />

          <motion.p
            className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            Create custom fashion that expresses your unique style.
            Where AI-powered design meets emotional connection.
          </motion.p>

          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
          >
            <Button size="lg" className="text-lg px-12">
              Start Creating
            </Button>

            <div className="flex flex-col md:flex-row gap-4 justify-center items-center text-sm text-gray-600">
              <motion.span
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="w-4 h-4" />
                AI-Powered Design
              </motion.span>
              <motion.span
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <Palette className="w-4 h-4" />
                Unlimited Customization
              </motion.span>
              <motion.span
                className="flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
              >
                <Heart className="w-4 h-4" />
                Express Yourself
              </motion.span>
            </div>
          </motion.div>

          {/* Status Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <Card variant="glass" className="max-w-md mx-auto">
              <CardContent className="text-center">
                <p className="text-gray-700">
                  🚧 <strong>Development in Progress</strong> — Building your dream fashion platform
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </Section>
    </main>
  );
}
