// Global type definitions for Ottiq

export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Design {
  id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  designData: any; // Konva/Fabric.js design data
  category: string;
  tags: string[];
  isPublic: boolean;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  userId: string;
  user?: User;
  createdAt: Date;
  updatedAt: Date;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
  totalAmount: number;
  currency: string;
  shippingAddress: Address;
  billingAddress?: Address;
  userId: string;
  user?: User;
  items: OrderItem[];
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  size?: string;
  color?: string;
  orderId: string;
  designId: string;
  design?: Design;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  name?: string;
  phone?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// UI Component Types
export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Design Editor Types
export interface DesignElement {
  id: string;
  type: 'text' | 'image' | 'shape' | 'pattern';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  opacity: number;
  visible: boolean;
  locked: boolean;
  data: any; // Element-specific data
}

export interface DesignCanvas {
  width: number;
  height: number;
  backgroundColor: string;
  elements: DesignElement[];
  layers: string[]; // Element IDs in layer order
}
